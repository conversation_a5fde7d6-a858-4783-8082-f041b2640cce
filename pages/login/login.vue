<template>
	<view class="container">
		<!-- 自定义导航栏 -->
		<view class='nav bg-white' :style="'height:'+navH+'px'">
			<view class='nav-title'>

			</view>
		</view>
		<!-- 内容主体 -->
		<scroll-view class='bg-gray overflow' :style="'height:calc(100vh - '+navH+'px)'" scroll-y>
			<view class="main-container">
				<view class="login-title">
					<view class="login-title-view">登录</view>
					<text class="login-title-text">欢迎使用通用航空运行管理监控系统</text>
				</view>
				<view class="login-container">
					<view class="inputNum" v-if="msmLogin">
						<view class="phone">
							<view class="phone-container">
								<text class="phone-container-text">手机号码</text>
								<input class="inputNum-input" type="number" maxlength="11" :value="phoneNum"
									:style="'border-bottom: 1px solid'+ warningColor"
									:class="[phoneNum.length==0?'active1':'active2' , {'warning-ture-color': isWarningColor}]"
								 @blur="mustFill" />
								<text class="iconfont icon-close-circle-fill" v-if="phoneNum.length"
									@click="clearValue"></text>
							</view>
							<view class="iconfont icon-a-LeftIcon" v-if="showWarning"><span
									class="warning-words">{{warningWords}}</span>
							</view>
						</view>
						<!-- 1 -->
						<view class="verification">
							<view>
								<text>验证码</text>
								<input class="verification-input" type="number" maxlength="6" :value="verCode"
									:style="'border-bottom: 1px solid' + vertifyWarningColor"
									:class="[inputValue1.length==0?'active1':'active2', {'vertify-warning-color': isVertifyWarningColor}]"
									 @blur="vertifyFill" />
							</view>
							<view class="iconfont icon-a-LeftIcon vertifyIcon" v-if="showVertifyWarning">
								<span class="vertify-warning-words">{{vertifyWarningWords}}</span>
							</view>
							<button @click="getVertify"
								:class="['van-btn','not-van-button', {'van-btn-color': isAbleBtn}]"><text
									:class="['btn-text',{'timer-color': vanTextColor}]">{{ remainStr }}</text>
							</button>
						</view>
					</view>
					<view class="login-in">
						<view class="weChat-in">
							<button style="background-color: #2979FF;color: #fff;" class="sys_btn" @click="vertifyLogin">
								进入首页
							</button>
						</view>
						<view class="" @click="outForLogin" style="text-align: center;text-decoration: #333;margin: 15px 0 ;">
							跳过登录
						</view>
					</view>
					<view class="" style="font-size: 13px;">
						使用范围：体验范围提供给通航公司机组人员使用、对外无开放注册流程。
					</view>
				</view>
			</view>

		</scroll-view>

		<!-- 整体背景放在最后 -->
		<view class='bg-main'>
			<image class='background' src="../../static/images/bgPlane.jpg"></image>
		</view>
	</view>
</template>

<script>
	const App = getApp();
	import { log } from 'util';
	import { getVerificationCodeByPhoneNumber,login,authLogin } from '../../api/weChat.js'
	export default {
		data() {
			return {
				navH: 0,
				msmLogin:true,
				//输入验证
				showWarning: false,
				showVertifyWarning: false,
				warningWords: '',
				vertifyWarningWords: '',
				phoneNum: '', //获取验证码时判断手机号的输入情况
				verCode: '', //验证码
				remainTime: 61,
				remainStr: '获取验证码',
				clickAble: true,//获取验证码按钮是否可以点击
				textColor: '#2C5DE5',
				isAbleBtn: false,
				vanTextColor: false,
				isWarningColor: false,
				warningColor: '##',
				vertifyWarningColor: '##',
				isVertifyWarningColor: false,
				wxCode: '',
			}
		},
		onLoad: function(options) {
			this.$set(this.$data, 'navH', Number(App.globalData.navHeight));
			// this.codeLogin()
		},
		methods: {
      //监听是否输入值，该改变图标以及border颜色
      // getValue1(e) {
      //   var reg = /^1(?:3\d|4[4-9]|5[0-35-9]|6[67]|7[013-8]|8\d|9\d)\d{8}$/;
      //   this.inputValue = e.detail.value;
      //   if (!this.inputValue) {
      //     this.showWarning = false;
      //     this.isWarningColor = false;
      //   }
      // },
      // getValue2(e) {
      //   this.inputValue1 = e.detail.value;
      // },
      //点击图标，输入清空
      clearValue() {
        this.inputValue = '';
        this.phoneNum = '';
        this.showWarning = false;
        this.isWarningColor = false;
      },
      //失去焦点触发-手机号
      mustFill(e) {
        this.phoneNum = e.detail.value;
      },
      //失去焦点触发-验证码
      vertifyFill(e) {
        this.showVertifyWarning = false;
        this.isVertifyWarningColor = false;
        this.vertifyValue = e.detail.value;
      },
      //点击验证码时查看手机号是否正确——验证码登录
      async getVertify() {
        if (!this.clickAble) return;
        const reg = /^1(?:3\d|4[4-9]|5[0-35-9]|6[67]|7[013-8]|8\d|9\d)\d{8}$/;
        if (!this.phoneNum) {
          this.showWarning = true;
          this.isWarningColor = true;
          this.warningWords = '请输入手机号';
        } else if (this.phoneNum.length != 11 || !reg.test(this.phoneNum)) {
          this.showWarning = true;
          this.isWarningColor = true;
          this.warningWords = '手机号不正确';
        } else {
          //请求接口获取验证码
          this.showVertifyWarning = false;
          this.showWarning = false;
          this.isWarningColor = false;
          this.isVertifyWarningColor = false;
          let param = {
            phoneNumber: this.phoneNum
          };
          try {
            const data = await getVerificationCodeByPhoneNumber(param);
            uni.showToast({
              title: '已发送验证码',
              icon: 'none',
            })
            this.isAbleBtn = true;
            this.vanTextColor = true;
            this.remainStr = '61s';
            this.setTimer();
          } catch (e) {
            uni.showToast({
              title: '短信验证码发送失败，请稍后再试',
              icon: 'none',
            })
            this.clickAble = true;
          }
        }
      },
      //倒计时
      setTimer() {
        if (this.remainTime == 0) {
          this.remainStr = '获取验证码';
          this.remainTime = 61;
          this.clickAble = true;
          this.isAbleBtn = false;
          this.vanTextColor = true;
        } else {
          this.clickAble = false;
          this.remainTime--;
          this.remainStr = this.remainTime + 's';
          setTimeout(() => {
            this.setTimer();
          }, 1000)
        }
      },
      //登录1-手机号登录
      async vertifyLogin() {
        var reg = /^1(?:3\d|4[4-9]|5[0-35-9]|6[67]|7[013-8]|8\d|9\d)\d{8}$/;
        console.log(this.inputValue1,this.clickAble)
        if (this.phoneNum == '') {
          this.showWarning = true;
          this.isWarningColor = true;
          this.warningWords = '请输入手机号';
        } else if (this.phoneNum.length != 11 || !reg.test(this.phoneNum)) {
          this.showVertifyWarning = false;
          this.isVertifyWarningColor = false;
          this.showWarning = true;
          this.isWarningColor = true;
          this.warningWords = '手机号不正确';
        } else if (this.inputValue1 == '' || this.clickAble) {
          this.showVertifyWarning = true;
          this.isVertifyWarningColor = true;
          this.vertifyWarningWords = '请先获取验证码';
        } else {
          let param = {
            loginType: 1,
            phoneNumber: this.phoneNum,
            verificationCode: this.vertifyValue
          }
          try {
            const data = await login(param);
            //将token存到本地存储中
            uni.setStorageSync('token', data.response.data.token);
            //将用户信息存到本地存储
            uni.setStorageSync('userInfo', data.response.data.userVO);
            uni.showToast({
              title: '登录成功',
              icon: 'none'
            })
            setTimeout(() => {
              uni.switchTab({
                url: '/pages/home/<USER>'
              })
            }, 1000)
          } catch (e) {
            console.log('getToken fail')
          }
        }
      },
      //微信登录
/*			codeLogin(){
	 			uni.login({
				  provider: 'weixin', //使用微信登录
				  success: async function (loginRes) {
					  console.log(loginRes)
					let param = {
						code: loginRes.code
					};

					try {
						const data = await authLogin(param);
						console.log(data.response.data.userVO)
						uni.setStorageSync('openId',data.response.data.openId)
						// 将token存到本地存储中
						uni.setStorageSync('token', data.response.data.token);
						//将用户信息存到本地存储
						uni.setStorageSync('userInfo', data.response.data.userVO);
						if(data.response.data.wxMenus!=null && data.response.data.wxMenus.length>0){
							let colorList =  [  'waiting', 'info', 'search', 'success','warn',  'download']
							data.response.data.wxMenus.map((i,o)=>{
								i['menuiconsweb'] = colorList[o]
							})
						}
						console.log(data.response.data.wxMenus)
						uni.setStorageSync('wxMenus', data.response.data.wxMenus);
						uni.setStorageSync('userRole', data.response.data.userRole || '');
						if(data.response.data.authStatus == 0 || data.response.data.authStatus == 2){
							uni.showToast({
								title: '暂无权限，跳转至申请页面！',
								icon: 'none'
							})
							setTimeout(() => {
								uni.navigateTo({
									url: '/pages/login/approal'
								})
							}, 1000)
						}else{

							uni.showToast({
								title: '登录成功',
								icon: 'none'
							})
							setTimeout(() => {
								uni.switchTab({
									url: '/pages/home/<USER>'
								})
							}, 1000)
						}
					} catch (e) {
						uni.showToast({
							title: '获取失败，请稍后再试',
							icon: 'none',
						})
					}
				  }
				});
			},*/
			outForLogin(){
				uni.navigateTo({
					url: '/pages/login/visit'
				});
			},
		}
	}
</script>

<style lang="less" scoped>
	.container {
		position: relative;
		height: 100vh;
	}

	page {
		height: 100%;
	}

	.background {
		width: 100%;
		// position: fixed;
		height: auto;
		z-index: -1;
		position: absolute;
		top: 0px;
		bottom: 0px;
	}

	.nav {
		width: 100%;
		overflow: hidden;
		position: relative;
		top: 0;
		left: 0;
		z-index: 10;
	}

	.nav-title {
		width: 100%;
		/* height: 45px;
		line-height: 45px; */
		text-align: center;
		position: absolute;
		bottom: 16px;
		left: 0;
		z-index: 10;
		font-family: OPPOSans;
		font-size: 16px;
		font-weight: bold;
		letter-spacing: 2px;

		text:first-child {
			position: relative;
			right: 125px;
		}
	}

	// van-button
	.time-interval {
		width: 104px;
		text-align: center;

		.van-button--default {
			width: 100%;
			background: none !important;
			padding: unset !important;
			outline: none !important;
			border: none !important;
		}
	}

	// 按钮
	.btnActive {
		width: 104px !important;
		border: 1px solid #ABCAFF !important;
		box-sizing: border-box !important;
		border-radius: 4px !important;
	}

	//输入框下面的颜色
	.active1 {
		border-bottom: 1px solid #D9DADD;
	}

	.active2 {
		border-bottom: 1px solid #2C5DE5;
	}

	.main-container {
		position: absolute;
		bottom: 70px;
		width: 100%;
		height: auto;
		box-sizing: border-box;

		.login-title {
			width: 100%;
			padding-left: 32px;
			font-family: OPPOSans;
			font-style: normal;
			font-weight: normal;
			margin: 4px auto;

			.login-title-view {
				font-size: 24px;
				line-height: 36px;
				color: #2C5DE5;
			}

			.login-title-text {
				font-size: 14px;
				line-height: 20px;
				color: #50545E;
				order: 1;
			}
		}

		.login-container {
			padding: 0 16px;

			.inputNum {
				display: flex;
				flex-direction: column;
				align-items: flex-start;
				padding: 24px 0;
				background: rgba(255, 255, 255, 0.64);
				border-radius: 8px;
				align-self: stretch;
				flex-grow: 0;
				margin: 32px auto;
				padding: 24px 16px;
				box-sizing: border-box;

				.inputNum-input {
					height: 35px;
					width: 100%;
					// border-bottom: 1px solid #D9DADD;
				}

				.phone {
					width: 100%;
					position: relative;
					margin-bottom: 24px;

					.phone-container {
						width: 100%;
						position: relative;

						.input {
							position: relative;
						}

						.phone-container-text {
							&:last-child {
								position: absolute;
								width: 16px;
								height: 16px;
								bottom: 8px;
								right: 0;
								z-index: 99;
								color: #BEC0C5;
							}
						}

						.icon-close-circle-fill {
							position: absolute;
							right: 0;
							bottom: 6px;
							z-index: 999;
							color: #BEC0C5;
						}
					}

					.icon-a-LeftIcon {
						position: absolute;
						color: #E83F4E;
						display: flex;
						align-items: center;
						margin-top: 4px;

						.warning-words {
							margin-left: 5px;
							font-size: 10px;
							margin: 0px 4px;
						}
					}
				}

				.verification {
					width: 100%;
					display: flex;
					align-items: flex-end;
					justify-content: space-between;
					position: relative;

					.verification-input {
						height: 35px;
						width: 100%;
					}

					.vertifyIcon {
						position: absolute;
						color: #E83F4E;
						display: flex;
						align-items: center;
						bottom: -20px;

						.vertify-warning-words {
							margin-left: 5px;
							font-size: 10px;
							margin: 0px 4px;
						}
					}

					.van-btn {
						border: 1px solid #2C5DE5;
						box-sizing: border-box;
						border-radius: 4px;
						white-space: nowrap;
						height: 40px;
						line-height: 40px;
						text-align: center;
						margin: 0;

						.van-btn-text {
							color: #2C5DE5;
							font-size: 14px;
						}
					}

					.van-btn-color {
						border: 1px solid #ABCAFF;
					}
				}

			}

			.login-in {
				.log-in {
					display: flex;
					flex-direction: row;
					justify-content: center;
					align-items: center;
					padding: 0px 16px;
					height: 40px;
					background: #2C5DE5;
					border-radius: 4px;
					color: #FFFFFF;
					margin: 10px 0px;
				}

				.split-line {
					position: relative;
					text-align: center;

					.or {
						&::before {
							position: absolute;
							left: 0;
							content: '';
							width: 150px;
							height: 1px;
							top: 9.5px;
							background: #D9DADD;
							flex-grow: 1;
						}

						&::after {
							position: absolute;
							right: 0;
							content: '';
							width: 150px;
							height: 1px;
							top: 9.5px;
							background: #D9DADD;
							flex-grow: 1;
						}
					}
				}

				.weChat-in {
					display: flex;
					justify-content: center;
					align-items: center;
					height: 40px;
					border: 1px solid #D9DADD;
					box-sizing: border-box;
					border-radius: 4px;
					order: 2;
					margin: 12px 0px;

					.sys_btn {
						position: relative;
						width: 100%;
						padding: unset;
						outline: none;
						border: none;
						height: 40px;
						line-height: 40px;

						.weChat-img {
							width: 18px;
							margin-right: 8px;
						}

						.url_btn {
							position: absolute;
							width: 100%;
							height: 40px;
							line-height: 40px;
						}
					}
				}
			}
		}
	}

	.warning-ture-color {
		border-bottom: 1px solid #E83F4E;
	}

	.vertify-warning-color {
		border-bottom: 1px solid #E83F4E;
	}


	.not-van-button {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		padding: 0px 16px;
		width: 102px;
		height: 40px;
		border: 1px solid #2C5DE5;
		box-sizing: border-box;
		border-radius: 4px;
		margin: 10px 0px;

		.btn-text {
			color: #2C5DE5;
			font-weight: 600;
			font-size: 14px;
			margin: 0px 8px;
		}

		.timer-color {
			color: #ABCAFF;
		}
	}
</style>
